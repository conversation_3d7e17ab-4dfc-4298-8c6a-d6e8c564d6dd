import { writable } from 'svelte/store';
import ps from './persistantStorage';

export type SearchMetrics = {
  itemCount: number;
  searchTime: number;
  hasSearched: boolean;
};

export type SearchStateData = {
  searchInput: string;
  tableSearchResults: any[];
  searchMetrics: SearchMetrics;
  activeRow: any;
};

// Default search state
const defaultSearchState: SearchStateData = {
  searchInput: "",
  tableSearchResults: [],
  searchMetrics: {
    itemCount: 0,
    searchTime: 0,
    hasSearched: false
  },
  activeRow: {}
};

// Create the writable store
const { subscribe, set, update } = writable<SearchStateData>(defaultSearchState);

// Load initial state from persistent storage
let initialized = false;

async function initializeSearchState() {
  if (initialized) return;

  try {
    const savedState = await ps.getSearchState();
    if (savedState) {
      console.log('Loading saved search state:', savedState);
      set(savedState);
    } else {
      console.log('No saved search state found, using default');
    }
    initialized = true;
  } catch (error) {
    console.error('Failed to load search state:', error);
    initialized = true;
  }
}

// Custom store with persistence
export const searchState = {
  subscribe,
  
  // Initialize the store (call this when the app starts)
  init: initializeSearchState,
  
  // Set search input and persist
  setSearchInput: async (input: string) => {
    update(state => {
      const newState = { ...state, searchInput: input };
      console.log('Saving search input:', input);
      ps.setSearchState(newState).catch(console.error);
      return newState;
    });
  },
  
  // Set search results and persist
  setSearchResults: async (results: any[]) => {
    update(state => {
      const newState = { ...state, tableSearchResults: results };
      ps.setSearchState(newState).catch(console.error);
      return newState;
    });
  },
  
  // Set search metrics and persist
  setSearchMetrics: async (metrics: SearchMetrics) => {
    update(state => {
      const newState = { ...state, searchMetrics: metrics };
      ps.setSearchState(newState).catch(console.error);
      return newState;
    });
  },
  
  // Set active row and persist
  setActiveRow: async (row: any) => {
    update(state => {
      const newState = { ...state, activeRow: row };
      ps.setSearchState(newState).catch(console.error);
      return newState;
    });
  },
  
  // Update multiple fields at once
  updateState: async (updates: Partial<SearchStateData>) => {
    update(state => {
      const newState = { ...state, ...updates };
      ps.setSearchState(newState).catch(console.error);
      return newState;
    });
  },
  
  // Clear all search state
  clear: async () => {
    set(defaultSearchState);
    await ps.setSearchState(defaultSearchState);
  },
  
  // Reset to default state
  reset: () => {
    set(defaultSearchState);
    ps.setSearchState(defaultSearchState).catch(console.error);
  }
};
